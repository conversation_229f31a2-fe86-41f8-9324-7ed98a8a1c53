<div class="new-project-container" *ngIf="projectsLoaded">
  <div class="new-project" id="new-project">
    <div class="projects-title-container">
      <span class="projects-title">{{ "homepage.projects" | translate }}</span>
      <div
        [matTooltip]="
          remainingProjects === 0
            ? ('homepage.limitReached' | translate)
            : ('homepage.newProjectTooltip' | translate)
        "
      >
        <button
          [disabled]="remainingProjects === 0"
          (click)="createNewProjectDialog()"
          class="projects-button"
          color="accent"
          id="newButton"
          mat-flat-button
        >
          {{ "homepage.newProject" | translate }}
        </button>
      </div>
      <fa-icon
        [matTooltip]="'tooltip.showExplanation' | translate"
        [icon]="['fal', 'question-circle']"
        size="lg"
        class="icon-explanation"
        (click)="
          isExplanationDisplayed
            ? closeHelpbox()
            : getExplanation('projects', 'expNewProject', false)
        "
      ></fa-icon>
    </div>
  </div>
  <div id="intro-div">
    <span class="introduction-text">{{
      "homepage.description" | translate
    }}</span>
  </div>

  <div class="input-search-title">
    <span class="h2-section-title">{{
      "homepage.recentProjects" | translate
    }}</span>
  </div>
  <ng-container
    *ngTemplateOutlet="
      projectsGrid;
      context: { dataSource: userProjects, owned: true }
    "
  ></ng-container>

  <div class="input-search-title">
    <span class="h2-section-title">{{
      "homepage.sharedWithMe" | translate
    }}</span>
  </div>
  <ng-container
    *ngTemplateOutlet="projectsGrid; context: { dataSource: sharedProjects }"
  ></ng-container>
</div>
<div class="page-spinner-container" *ngIf="!projectsLoaded">
  <mat-spinner diameter="96"></mat-spinner>
</div>

<ng-template #projectsGrid let-dataSource="dataSource" let-owned="owned">
  <div class="projects-grid">
    <tx-grid
      #projectsGrid
      [data]="dataSource"
      [columns]="txGridColumns"
      [primaryKey]="'_id'"
      [enablePagination]="true"
      [pageSize]="50"
    >
      <!-- Project Name Column Template -->
      <tx-grid-column fieldName="name">
        <ng-template let-data>
          <span
            (click)="openProject(data)"
            class="project-name-link"
            [matTooltip]="'homepage.openProject' | translate"
            matTooltipPosition="right"
          >
            {{ data.name }}
          </span>
        </ng-template>
      </tx-grid-column>

      <!-- Dataset Source Column Template -->
      <tx-grid-column fieldName="dataset_source">
        <ng-template let-data>
          <div
            *ngIf="
              data?.dataset_source !== 'teexma' &&
              (data?.dataset_source?.endsWith('.xlsm') ||
                data?.dataset_source?.endsWith('.xlsx'))
            "
          >
            <span>
              <p style="display: inline; font-weight: bold">
                {{ "homepage.excelFile" | translate }}
              </p>
            </span>
          </div>

          <div
            *ngIf="
              data?.dataset_source !== 'teexma' &&
              data?.dataset_source?.endsWith('.csv')
            "
          >
            <span>
              <p style="display: inline; font-weight: bold">
                {{ "homepage.csvFile" | translate }}
              </p>
            </span>
          </div>

          <div
            *ngIf="
              data.dataset_source === 'teexma' &&
              data.dataset_source.endsWith('.csv')
            "
          >
            <span>
              <div style="display: inline; margin-right: 16px">
                <fa-icon [icon]="['fal', 'file-csv']" size="lg"></fa-icon>
              </div>
              <p style="display: inline; font-weight: bold">
                {{ data.dataset_source }}
              </p>
            </span>
          </div>

          <div
            *ngIf="
              data.dataset_source === 'teexma' &&
              (data.dataset_source.endsWith('.xlsm') ||
                data.dataset_source.endsWith('.xlsx'))
            "
          >
            <span>
              <div style="display: inline; margin-right: 16px">
                <fa-icon [icon]="['fal', 'file-excel']" size="lg"></fa-icon>
              </div>
              <p style="display: inline; font-weight: bold">
                {{ data.dataset_source }}
              </p>
            </span>
          </div>

          <div
            *ngIf="
              data.dataset_source === 'teexma' &&
              !data.dataset_source.endsWith('.csv') &&
              !(data.dataset_source === 'teexma') &&
              !data.dataset_source.endsWith('.xlsm') &&
              !data.dataset_source.endsWith('.xlsx')
            "
          >
            <span>
              <div style="display: inline; margin-right: 16px">
                <fa-icon [icon]="['fal', 'file']" size="lg"></fa-icon>
              </div>
              <p style="display: inline; font-weight: bold">
                {{ data.dataset_source }}
              </p>
            </span>
          </div>

          <div *ngIf="data.dataset_source === 'teexma'" style="display: inline">
            <span style="display: inline">
              <div style="display: inline; margin-right: 10px">
                <p style="display: inline; font-weight: bold">T</p>
                <p style="color: #f46e1b; display: inline; font-weight: bold">
                  X
                </p>
              </div>
              <p style="display: inline">TEE</p>
              <p style="color: #f46e1b; display: inline; font-weight: bold">
                X
              </p>
              <p style="display: inline">MA<sup>®</sup></p>
            </span>
          </div>
        </ng-template>
      </tx-grid-column>

      <!-- Last Opened Date Column Template -->
      <tx-grid-column fieldName="last_opened">
        <ng-template let-data>
          {{ data.last_opened.$date | date : "dd/MM/yyyy HH:mm:ss" }}
        </ng-template>
      </tx-grid-column>

      <!-- Creation Date Column Template -->
      <tx-grid-column fieldName="creation_date">
        <ng-template let-data>
          {{ data.creation_date.$date | date : "dd/MM/yyyy HH:mm:ss" }}
        </ng-template>
      </tx-grid-column>

      <!-- Owner Column Template -->
      <tx-grid-column fieldName="owner">
        <ng-template let-data>
          {{ data.owner.name }}
        </ng-template>
      </tx-grid-column>

      <!-- URL Column Template -->
      <tx-grid-column fieldName="url">
        <ng-template let-data>
          <fa-icon
            (click)="copyAnalysisUrl(data)"
            [icon]="['fal', 'link']"
            class="action-icon"
            size="lg"
            [matTooltip]="'mainNav.copyAnalysisUrl' | translate"
            matTooltipPosition="right"
          >
          </fa-icon>
        </ng-template>
      </tx-grid-column>

      <tx-grid-column fieldName="duplicate">
        <ng-template let-data>
          <fa-icon
            (click)="duplicateProject(data)"
            [icon]="['fal', 'copy']"
            class="action-icon"
            size="lg"
            [matTooltip]="'homepage.duplicateProject' | translate"
            matTooltipPosition="right"
          >
          </fa-icon>
        </ng-template>
      </tx-grid-column>

      <tx-grid-column fieldName="status">
        <ng-template let-data>
          <fa-icon
            *ngIf="owned || connectedUser?.isAdmin"
            (click)="onIconClicked(data)"
            [icon]="['fal', 'trash-alt']"
            class="action-icon delete-icon"
            size="lg"
            [matTooltip]="'homepage.confirmDeletion' | translate"
            matTooltipPosition="right"
          >
          </fa-icon>
        </ng-template>
      </tx-grid-column>
    </tx-grid>
  </div>
</ng-template>

<app-dialog-new-project
  (projectCreated)="openProject($event)"
></app-dialog-new-project>
<app-dialog-delete
  (confirm)="deleteProject()"
  [data]="this.projectDeletedData"
></app-dialog-delete>
